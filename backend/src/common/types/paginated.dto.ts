import { ApiProperty } from '@nestjs/swagger';

export class PaginatedMeta {
  @ApiProperty({ description: 'Total number of items' })
  totalItems: number;

  @ApiProperty({ description: 'Current page number' })
  currentPage: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ description: 'Number of items per page' })
  itemsPerPage: number;

  @ApiProperty({ description: 'Whether this is the first page' })
  isFirstPage: boolean;

  @ApiProperty({ description: 'Whether this is the last page' })
  isLastPage: boolean;

  @ApiProperty({ description: 'Whether there is a next page' })
  hasNextPage: boolean;

  @ApiProperty({ description: 'Whether there is a previous page' })
  hasPreviousPage: boolean;

  @ApiProperty({ description: 'Next page number', required: false })
  nextPage?: number;

  @ApiProperty({ description: 'Previous page number', required: false })
  previousPage?: number;

  constructor(
    totalItems: number,
    currentPage: number,
    itemsPerPage: number,
  ) {
    this.totalItems = totalItems;
    this.currentPage = currentPage;
    this.itemsPerPage = itemsPerPage;
    this.totalPages = Math.ceil(totalItems / itemsPerPage);
    this.isFirstPage = currentPage === 1;
    this.isLastPage = currentPage === this.totalPages;
    this.hasNextPage = currentPage < this.totalPages;
    this.hasPreviousPage = currentPage > 1;
    this.nextPage = this.hasNextPage ? currentPage + 1 : undefined;
    this.previousPage = this.hasPreviousPage ? currentPage - 1 : undefined;
  }
}

export class Paginated<T> {
  @ApiProperty({ description: 'Array of items for the current page', isArray: true })
  data: T[];

  @ApiProperty({ description: 'Pagination metadata', type: PaginatedMeta })
  meta: PaginatedMeta;

  constructor(data: T[], meta: PaginatedMeta) {
    this.data = data;
    this.meta = meta;
  }

  static create<T>(
    data: T[],
    totalItems: number,
    currentPage: number,
    itemsPerPage: number,
  ): Paginated<T> {
    const meta = new PaginatedMeta(totalItems, currentPage, itemsPerPage);
    return new Paginated(data, meta);
  }
}
