import { ApiProperty } from '@nestjs/swagger';

export class ApiResponse<T> {
  @ApiProperty({ description: 'Indicates if the request was successful' })
  success: boolean;

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiProperty({ description: 'Response data', required: false })
  data?: T;

  @ApiProperty({ description: 'Error details', required: false })
  error?: string;

  @ApiProperty({ description: 'Timestamp of the response' })
  timestamp: string;

  @ApiProperty({ description: 'Request path', required: false })
  path?: string;

  constructor(
    success: boolean,
    message: string,
    data?: T,
    error?: string,
    path?: string,
  ) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.error = error;
    this.timestamp = new Date().toISOString();
    this.path = path;
  }

  static success<T>(
    data: T,
    message: string = 'Request successful',
    path?: string,
  ): ApiResponse<T> {
    return new ApiResponse(true, message, data, undefined, path);
  }

  static error<T>(
    error: string,
    message: string = 'Request failed',
    path?: string,
  ): ApiResponse<T> {
    return new ApiResponse(false, message, undefined, error, path);
  }
}
