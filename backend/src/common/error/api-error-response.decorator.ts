import { applyDecorators } from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';
import { ErrorCode } from '@/common';
import { ApiErrorResponseDto } from './api-error-response.dto';

/**
 * Decorator để gắn thông tin về các error response có thể xuất hiện từ API
 * @param errorCodes Các mã lỗi có thể xảy ra
 * @returns Swagger decorator
 */
export function ApiErrorResponse(...errorCodes: ErrorCode[]): MethodDecorator {
  const decorators = errorCodes.map((errorCode) => {
    return ApiResponse({
      status: errorCode.status,
      description: `${errorCode.message} (Code: ${errorCode.code})`,
      schema: {
        $ref: getModelSchemaRef(ApiErrorResponseDto),
      },
    });
  });

  return applyDecorators(...decorators);
}

/**
 * Helper để lấy tham chiếu đến schema của model
 * @param model Class model
 * @returns Schema reference string
 */
function getModelSchemaRef(model: any): string {
  const modelName = model.name;
  return `#/components/schemas/${modelName}`;
} 