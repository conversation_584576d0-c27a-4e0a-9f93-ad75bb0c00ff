import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class LoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger(LoggingMiddleware.name);

  use(req: Request, res: Response, next: NextFunction): void {
    const { method, originalUrl, ip, headers } = req;
    const userAgent = headers['user-agent'] || '';
    const startTime = Date.now();

    // Log incoming request
    this.logger.log(
      `Incoming Request: ${method} ${originalUrl} - IP: ${ip} - User-Agent: ${userAgent}`,
    );

    // Override res.end to capture response details
    const originalEnd = res.end;
    res.end = function (chunk?: any, encoding?: any, cb?: any) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      const { statusCode } = res;
      const contentLength = res.get('content-length') || 0;

      // Determine log level based on status code
      const logLevel = statusCode >= 400 ? 'error' : statusCode >= 300 ? 'warn' : 'log';
      
      const logMessage = `Response: ${method} ${originalUrl} - Status: ${statusCode} - ${responseTime}ms - ${contentLength} bytes`;

      if (logLevel === 'error') {
        Logger.error(logMessage, LoggingMiddleware.name);
      } else if (logLevel === 'warn') {
        Logger.warn(logMessage, LoggingMiddleware.name);
      } else {
        Logger.log(logMessage, LoggingMiddleware.name);
      }

      // Call the original end method
      originalEnd.call(this, chunk, encoding, cb);
    };

    next();
  }
}
