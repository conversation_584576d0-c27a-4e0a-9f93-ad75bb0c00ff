/**
 * Constant object for Swagger API tags
 * Used to ensure consistency in API documentation
 */
export const SwaggerApiTag = {
  USERS: 'Users',
  COMMON: 'Common',
  ADMIN_USERS: 'Admin - Users',
  EMPLOYEES: 'Employees',
  TESTS: 'Tests',
  ADMIN_TESTS: 'Admin - Tests',
  ADMIN_FILES: 'Admin - Files',
  SETTINGS: 'Settings',
  ADMIN_SETTINGS: 'Admin - Settings',
  RESOURCES: 'Resources',
  BLOGS: 'Blogs',
  BLOG_COMMENTS: 'Blog Comments',
  BLOG_PURCHASES: 'Blog Purchases',
  ADMIN_BLOGS: 'Admin - Blogs',
  ADMIN_MEDIA: 'Admin - Media',
  ADMIN_URL: 'Admin - URL',
  MODEL_TRAINING: 'Model Training',
  ADMIN_MODEL_TRAINING: 'Admin - Model Training',
  USER_SUBSCRIPTIONS: 'User - Subscriptions',
  ADMIN_SUBSCRIPTION_PLAN: 'Admin - Subscription Plans',
  ADMIN_SUBSCRIPTION_PLAN_PRICING: 'Admin - Subscription Plan Pricing',
  ADMIN_SUBSCRIPTION: 'Admin - Subscriptions',
  ADMIN_SUBSCRIPTION_ORDER: 'Admin - Subscription Orders',
  USER_STRATEGY: 'User Strategy',
  ADMIN_STRATEGY: 'Admin - Strategy',
  INTEGRATION: 'Integration',
  INTEGRATION_ADMIN: 'Admin - Integration',
  USER_KNOWLEDGE_FILES: 'User - Knowledge Files',
  ADMIN_KNOWLEDGE_FILES: 'Admin - Knowledge Files',
  BANKS: 'Banks',
  USER_BANKS: 'User Banks',
  USER_AUDIENCE: 'User - Audience',
  USER_CAMPAIGN: 'User - Campaign',
  USER_TEMPLATE_EMAIL: 'User - Template Email',
  USER_SEGMENT: 'User - Segment',
  USER_TAG: 'User - Tag',
  USER_TEMPLATE_SMS: 'User - Template SMS',
  USER_ACCOUNT: 'User - Account',
  USER_AFFILIATE: 'User - Affiliate',
  USER_AFFILIATE_STATISTICS: 'User - Affiliate Statistics',
  USER_AFFILIATE_ACCOUNT: 'User - Affiliate Account',
  USER_AFFILIATE_ORDER: 'User - Affiliate Order',
  USER_AFFILIATE_CUSTOMER: 'User - Affiliate Customer',
  USER_AFFILIATE_POINT_CONVERSION: 'User - Affiliate Point Conversion',
  USER_AFFILIATE_REFERRAL_LINK: 'User - Affiliate Referral Link',
  ADMIN_AFFILIATE_ACCOUNT: 'Admin - Affiliate Account',
  ADMIN_AFFILIATE_ORDER: 'Admin - Affiliate Order',
  ADMIN_AFFILIATE_WITHDRAWAL: 'Admin - Affiliate Withdrawal',
  ADMIN_AFFILIATE_CUSTOMER: 'Admin - Affiliate Customer',
  ADMIN_AFFILIATE_OVERVIEW: 'Admin - Affiliate Overview',
  ADMIN_AFFILIATE_RANK: 'Admin - Affiliate Rank',
  USER_MARKETING_STATISTICS: 'User - Marketing Statistics',
  USER_AFFILIATE_WITHDRAWAL: 'User - Affiliate Withdrawal',
  USER_AFFILIATE_UPLOAD: 'User - Affiliate Upload',
  USER_AFFILIATE_BUSINESS: 'User - Affiliate Business',
  USER_AFFILIATE_REGISTRATION: 'User - Affiliate Registration',
  ZALO: 'User - Zalo',
  ZALO_ZNS: 'User - Zalo ZNS',
  ZALO_SEGMENT: 'User - Zalo Segment',
  ZALO_CAMPAIGN: 'User - Zalo Campaign',
  ZALO_AUTOMATION: 'User - Zalo Automation',
  ZALO_INTEGRATION: 'User - Zalo Integration',
  ADMIN_AFFILIATE_CONTRACT: 'Admin - Affiliate Contract',
  EMAIL: 'Email',
  DATA_STATISTICS: 'Data - Statistics',

  // ADMIN
  ADMIN_USER: 'Admin - User',
  ADMIN_SEGMENT: 'Admin - Segment',
  ADMIN_TAG: 'Admin - Tag',
  ADMIN_AUDIENCE: 'Admin - Audience',
  ADMIN_TEMPLATE_EMAIL: 'Admin - Template Email',
  ADMIN_CAMPAIGN: 'Admin - Campaign',
  USER_TYPE_AGENT: 'User - Type Agent',
  ADMIN_TYPE_AGENT: 'Admin - Type Agent',
  ADMIN_AFFILIATE: 'Admin - Affiliate',
  ADMIN_AFFILIATE_CLICK: 'Admin - Affiliate Click',
  ADMIN_AFFILIATE_POINT_CONVERSION: 'Admin - Affiliate Point Conversion',
  USER_TOOL: 'User - Tool',
  ADMIN_TOOL: 'Admin - Tool',
  ADMIN_AGENT_SYSTEM: 'Admin - Agent System',
  ADMIN_AGENT_BASE: 'Admin - Agent Base',
  ADMIN_AGENT_TEMPLATE: 'Admin - Agent Template',
  ADMIN_AGENT_RESOURCES: 'Admin - Agent Resources',
  ADMIN_AGENT_ROLE: 'Admin - Agent Role',
  USER_AGENT: 'User - Agent',
  USER_AGENT_RESOURCES: 'User - Agent Resources',
  ADMIN_AGENT_PERMISSION: 'Admin - Agent Permission',
  ADMIN_BUSINESS: 'Admin - Business',
  ADMIN_BUSINESS_WAREHOUSE: 'Admin - Business - Warehouse',
  ADMIN_BUSINESS_FILE: 'Admin - Business - File',
  ADMIN_BUSINESS_FOLDER: 'Admin - Business - Folder',
  // Marketplace tags
  USER_MARKETPLACE_PRODUCTS: 'Marketplace - Products',
  USER_MARKETPLACE_CART: 'Marketplace - Cart',
  USER_MARKETPLACE_ORDERS: 'Marketplace - Orders',
  ADMIN_MARKETPLACE_PRODUCTS: 'Admin - Marketplace Products',
  ADMIN_MARKETPLACE_CART: 'Admin - Marketplace Cart',
  ADMIN_MARKETPLACE_ORDERS: 'Admin - Marketplace Orders',
  // Business tags
  USER_BUSINESS: 'User - Business',
  ADMIN_GROUP_TOOL: 'Admin - Group Tool',
  ADMIN_VERSION_TOOL: 'Admin - Version Tool',
  USER_VERSION_TOOL: 'User - Version Tool',
  USER_GROUP_TOOL: 'User - Group Tool',
  USER_RULE_CONTRACT: 'User - Rule Contract',
  USER_ORDER: 'User - Order',
  USER_CONVERT: 'User - Convert',
  USER_CONVERT_CUSTOMER: 'User - Convert Customer',
  // Warehouse
  USER_WAREHOUSE_PHYSICAL:'User Physical Warehouse',
  USER_WAREHOUSE_VIRTUAL:'User Virtual Warehouse',
  USER_WAREHOUSE_VIRTUAL_FILES:'User Virtual Warehouse Files',
  USER_WAREHOUSE_VIRTUAL_FOLDERS:'User Virtual Warehouse Folders',
  USER_WAREHOUSE:'User Warehouses',
  USER_WAREHOUSE_CUSTOM_FIELD:'User Warehouse Custom Field',
  // Marketing
  USER_TOOL_INTEGRATION: 'User - Tool Integration',
  MARKETING_ADMIN: 'Admin - Marketing',
  MARKETING_USER: 'User - Marketing',
  ADMIN_INTEGRATION_SMS_TEST: 'Admin - Integration - SMS Test',
  USER_URL: 'User - URL',
  AUTH: 'Authentication',
  USER_MARKETPLACE_PAYMENT: 'User - Marketplace Payment',
  R_POINT_ADMIN_COUPONS: 'R-Point - Admin Coupons',
  R_POINT_ADMIN_POINTS: 'R-Point - Admin Points',
  ADMIN_PROVIDER_MODEL: 'Admin - Provider Model',
  USER_PROVIDER_MODEL: 'User - Provider Model',
  PAYMENT_R_POINT_USER: 'Payment - R-Point - User',
  ADMIN_AGENT_RANK: 'Admin - Agent Rank',
  ADMIN_TASK: 'Admin - Task',
  USER_TASK: 'User - Task',
  ADMIN_AGENT: 'Admin - Agent User',
  EMPLOYEE_ROLES: 'Employee - Roles',
  EMPLOYEE_PERMISSIONS: 'Employee - Permissions',
  // INTEGRATION
  ADMIN_INTEGRATION: 'Admin - Integration',
  USER_INTEGRATION_FACEBOOK: 'User - Integration Facebook',
  USER_INTEGRATION_WEBSITE: 'User - Integration Website',
  USER_MEDIA: 'User - Media',
  ADMIN_RULE_CONTRACT: 'Admin - Rule Contract',

  // MODEL TRAINING
  USER_BASE_MODEL: 'User - Base Model',
  ADMIN_BASE_MODEL: 'Admin - Base Model',
  USER_FINETUNING: 'User - Finetuning',
  ADMIN_FINETUNING: 'Admin - Finetuning',
  USER_FINETUNING_DATA: 'User - Finetuning Data',
  ADMIN_FINETUNING_DATA: 'Admin - Finetuning Data',
  ADMIN_MODEL_FINE_TUNING: 'Admin - Model Finetuning ',
  ADMIN_BASE_MODEL_TRASH: 'Admin - Base Model Trash',

  // CHAT PANEL
  USER_CHAT: 'User - Chat',
  ADMIN_CHAT: 'Admin - Chat',
} as const;

export const SWAGGER_API_TAGS = SwaggerApiTag;
