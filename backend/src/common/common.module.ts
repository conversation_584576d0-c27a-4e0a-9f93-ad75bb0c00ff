import { Module, Global } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { GlobalExceptionFilter } from '@filters/global-exception.filter';
import { ErrorHandlingInterceptor } from '@interceptors/error-handling.interceptor';

/**
 * Module chung cung cấp các thành phần dùng chung cho toàn bộ ứng dụng
 */
@Global()
@Module({
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ErrorHandlingInterceptor,
    },
  ],
  exports: [],
})
export class CommonModule {}
