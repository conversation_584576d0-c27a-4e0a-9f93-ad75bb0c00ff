# Hướng dẫn sử dụng ApiResponseDto trong Swagger

File này cung cấp hướng dẫn cách sử dụng `ApiResponseDto` để hiển thị đúng trong Swagger.

## Cách sử dụng cơ bản

### 1. Đ<PERSON>ng ký các model với Swagger

Trước khi sử dụng `ApiResponseDto` trong controller, bạn cần đăng ký các model với Swagger bằng decorator `@ApiExtraModels`:

```typescript
import { ApiExtraModels } from '@nestjs/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { UserResponseDto } from './dto/user-response.dto';

@ApiExtraModels(ApiResponseDto, PaginatedResult, UserResponseDto)
@Controller('users')
export class UserController {
  // ...
}
```

### 2. Sử dụng ApiResponseDto.getSchema() cho response thông thường

```typescript
import { ApiResponseDto } from '@common/response';
import { UserResponseDto } from './dto/user-response.dto';

@Get(':id')
@ApiOperation({ summary: 'Lấy thông tin người dùng theo ID' })
@ApiResponse({
  status: 200,
  description: 'Thông tin người dùng',
  schema: ApiResponseDto.getSchema(UserResponseDto)
})
async getUserById(@Param('id') id: string): Promise<ApiResponseDto<UserResponseDto>> {
  const user = await this.userService.findById(id);
  return ApiResponseDto.success(user);
}
```

### 3. Sử dụng ApiResponseDto.getPaginatedSchema() cho response phân trang

```typescript
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { UserResponseDto } from './dto/user-response.dto';

@Get()
@ApiOperation({ summary: 'Lấy danh sách người dùng có phân trang' })
@ApiResponse({
  status: 200,
  description: 'Danh sách người dùng',
  schema: ApiResponseDto.getPaginatedSchema(UserResponseDto)
})
async getUsers(@Query() query: QueryUserDto): Promise<ApiResponseDto<PaginatedResult<UserResponseDto>>> {
  const users = await this.userService.findAll(query);
  return ApiResponseDto.paginated(users);
}
```

### 4. Sử dụng ApiResponseDto cho response không có dữ liệu

```typescript
@Delete(':id')
@ApiOperation({ summary: 'Xóa người dùng' })
@ApiResponse({
  status: 200,
  description: 'Người dùng đã được xóa thành công',
  schema: {
    allOf: [
      { $ref: getSchemaPath(ApiResponseDto) },
      {
        properties: {
          result: { type: 'null' },
          message: { example: 'Người dùng đã được xóa thành công' }
        }
      }
    ]
  }
})
async deleteUser(@Param('id') id: string): Promise<ApiResponseDto<null>> {
  await this.userService.delete(id);
  return ApiResponseDto.success(null, 'Người dùng đã được xóa thành công');
}
```

## Ví dụ đầy đủ cho một controller

```typescript
import { Controller, Get, Post, Body, Param, Delete, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiExtraModels, getSchemaPath } from '@nestjs/swagger';
import { UserService } from './user.service';
import { CreateUserDto, UpdateUserDto, QueryUserDto, UserResponseDto } from './dto';
import { ApiResponseDto, PaginatedResult } from '@common/response';

@ApiTags('Users')
@ApiExtraModels(ApiResponseDto, PaginatedResult, UserResponseDto)
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: 'Tạo người dùng mới' })
  @ApiResponse({
    status: 201,
    description: 'Người dùng đã được tạo thành công',
    schema: ApiResponseDto.getSchema(UserResponseDto)
  })
  async createUser(@Body() createUserDto: CreateUserDto): Promise<ApiResponseDto<UserResponseDto>> {
    const user = await this.userService.create(createUserDto);
    return ApiResponseDto.created(user);
  }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách người dùng có phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách người dùng',
    schema: ApiResponseDto.getPaginatedSchema(UserResponseDto)
  })
  async getUsers(@Query() query: QueryUserDto): Promise<ApiResponseDto<PaginatedResult<UserResponseDto>>> {
    const users = await this.userService.findAll(query);
    return ApiResponseDto.paginated(users);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin người dùng theo ID' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin người dùng',
    schema: ApiResponseDto.getSchema(UserResponseDto)
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy người dùng',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { example: 404 },
            message: { example: 'Không tìm thấy người dùng' },
            result: { type: 'null' }
          }
        }
      ]
    }
  })
  async getUserById(@Param('id') id: string): Promise<ApiResponseDto<UserResponseDto>> {
    const user = await this.userService.findById(id);
    return ApiResponseDto.success(user);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Người dùng đã được xóa thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { type: 'null' },
            message: { example: 'Người dùng đã được xóa thành công' }
          }
        }
      ]
    }
  })
  async deleteUser(@Param('id') id: string): Promise<ApiResponseDto<null>> {
    await this.userService.delete(id);
    return ApiResponseDto.success(null, 'Người dùng đã được xóa thành công');
  }
}
```

## Lưu ý quan trọng

1. Luôn đăng ký các model với `@ApiExtraModels` trước khi sử dụng chúng trong schema.
2. Sử dụng `ApiResponseDto.getSchema()` cho response thông thường.
3. Sử dụng `ApiResponseDto.getPaginatedSchema()` cho response phân trang.
4. Đối với response không có dữ liệu, bạn có thể sử dụng cấu trúc schema tùy chỉnh.
5. Đảm bảo kiểu dữ liệu trả về của phương thức controller khớp với schema đã khai báo.
